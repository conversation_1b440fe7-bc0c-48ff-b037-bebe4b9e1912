name: CI/CD
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
jobs:
  black-check:
    name: Black Check
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4.1.4
      - name: Set up Python 3.10
        uses: actions/setup-python@v5.1.0
        with:
          python-version: "3.10"
      - name: Install Black
        run: pip install black
      - name: Check Python files with Black
        run: black --check . --exclude ".*pb2.*\.py"
  install-dependencies:
    name: Install Python Dependencies
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4.1.4
      - name: Set up Python 3.10
        uses: actions/setup-python@v5.1.0
        with:
          python-version: "3.10"
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then
            pip install -r requirements.txt
          fi
  release:
    name: Release
    runs-on: ubuntu-latest
    needs:
      - black-check
      - install-dependencies
    if: github.repository == 'palp1tate/fetch-gushiwen' && github.event_name == 'push'
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.4
        with:
          fetch-depth: -1
      - name: Setup Node.js
        uses: actions/setup-node@v4.0.2
        with:
          node-version: 20
      - name: Fetch Previous version
        id: get-previous-tag
        uses: actions-ecosystem/action-get-latest-tag@v1.6.0
      - name: Release
        run: yarn global add semantic-release@23.1.1 && semantic-release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

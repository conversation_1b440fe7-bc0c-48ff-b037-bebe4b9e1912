# SQLite数据库配置说明

## 概述

本项目已成功从MySQL迁移到SQLite3数据库。SQLite是一个轻量级的嵌入式数据库，无需安装额外的数据库服务器，非常适合个人使用和学习。

## 已完成的修改

### 1. 配置文件更新
- `config.yaml` - 已配置为使用SQLite数据库
- 数据库文件路径：`gushiwen.db`

### 2. 数据模型优化
- `model.py` - 修改了数据类型以兼容SQLite：
  - 将MySQL特定的`LONGTEXT`改为通用的`Text`
  - 将`BigInteger`改为`Integer`以确保自增ID正常工作

### 3. 数据库工具完善
- `utils.py` - 改进了数据库引擎初始化逻辑
- `create_sqlite_db.py` - SQLite数据库创建脚本

### 4. 测试脚本
- `test_sqlite.py` - 数据库连接和操作测试脚本

## 使用方法

### 1. 安装依赖
```bash
pip3 install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
```

### 2. 创建数据库
```bash
python3 create_sqlite_db.py
```

### 3. 测试数据库
```bash
python3 test_sqlite.py
```

### 4. 使用爬虫保存到数据库

#### 爬取整页诗歌到数据库
```bash
python3 shige_db.py
```
然后输入古诗文网的页面链接，例如：
```
https://so.gushiwen.cn/gushi/tangshi.aspx
```

#### 爬取单首诗歌到数据库
```bash
python3 single_shige_db.py
```
然后输入单首诗歌的链接，例如：
```
https://so.gushiwen.cn/shiwenv_45c396367f59.aspx
```

## 数据库结构

SQLite数据库包含一个`poem`表，结构如下：

```sql
CREATE TABLE poem (
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(256),           -- 诗歌名称
    author VARCHAR(256),         -- 作者
    dynasty VARCHAR(256),        -- 朝代
    content TEXT,                -- 诗歌内容
    trans TEXT,                  -- 译文
    annotation TEXT,             -- 注释
    appreciation TEXT,           -- 赏析
    background TEXT,             -- 创作背景
    created_at DATETIME          -- 创建时间
);
```

## 数据库文件位置

- 数据库文件：`gushiwen.db`（位于项目根目录）
- 可以使用任何SQLite客户端工具查看和管理数据

## 常用SQLite命令

### 查看所有诗歌
```bash
sqlite3 gushiwen.db "SELECT name, author, dynasty FROM poem;"
```

### 查看特定作者的诗歌
```bash
sqlite3 gushiwen.db "SELECT name, content FROM poem WHERE author='李白';"
```

### 查看数据库表结构
```bash
sqlite3 gushiwen.db ".schema"
```

### 查看数据库中的记录数
```bash
sqlite3 gushiwen.db "SELECT COUNT(*) FROM poem;"
```

## 优势

1. **无需安装数据库服务器** - SQLite是嵌入式数据库
2. **轻量级** - 数据库文件可以轻松备份和迁移
3. **跨平台** - 在Windows、macOS、Linux上都能正常工作
4. **易于管理** - 单个文件包含整个数据库
5. **性能良好** - 对于中小型数据集性能优秀

## 注意事项

1. SQLite适合单用户使用，不支持高并发写入
2. 数据库文件需要定期备份
3. 如果需要迁移回MySQL，只需修改`config.yaml`并添加MySQL配置即可

## 故障排除

如果遇到问题，可以：

1. 删除`gushiwen.db`文件并重新运行`python3 create_sqlite_db.py`
2. 运行`python3 test_sqlite.py`检查数据库是否正常工作
3. 检查Python版本和依赖是否正确安装

## 项目文件说明

- `config.yaml` - 数据库配置文件
- `model.py` - 数据模型定义
- `utils.py` - 数据库工具函数
- `create_sqlite_db.py` - 数据库创建脚本
- `test_sqlite.py` - 数据库测试脚本
- `shige_db.py` - 批量爬取诗歌到数据库
- `single_shige_db.py` - 单首诗歌爬取到数据库
- `gushiwen.db` - SQLite数据库文件（运行后生成）

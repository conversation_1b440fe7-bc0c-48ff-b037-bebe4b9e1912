from sqlalchemy import (
    Column,
    String,
    Integer,
    DateTime,
    Text,
)
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class Poem(Base):
    __tablename__ = "poem"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(256))
    author = Column(String(256))
    dynasty = Column(String(256))
    content = Column(Text)
    trans = Column(Text)
    annotation = Column(Text)
    appreciation = Column(Text)
    background = Column(Text)
    created_at = Column(DateTime)

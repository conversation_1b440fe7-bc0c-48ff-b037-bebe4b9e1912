from sqlalchemy import create_engine
from model import Base
from utils import init_engine

def create_tables():
    """创建SQLite数据库表"""
    try:
        engine = init_engine()
        if engine is None:
            print("Failed to initialize the engine.")
            return False
            
        # 创建所有表
        Base.metadata.create_all(engine)
        print("SQLite database and tables created successfully!")
        print(f"Database file: gushiwen.db")
        return True
        
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

if __name__ == "__main__":
    create_tables()
# fetch-gushiwen

## 用途

可以拿去用于个人知识库、知识图谱的创建等其他学习用途。

## 前置条件

克隆项目：

```powershell
git clone https://github.com/palp1tate/fetch-gushiwen.git
```

进入项目根目录下载依赖：

```powershell
#新建虚拟环境并激活
python -m venv test_env
source test_env/bin/activate  # 在Unix或MacOS上
test_env\Scripts\activate     # 在Windows上

#如果不想新建虚拟环境可以省去上面的命令
pip install -r requirements.txt
```

如果你有保存诗歌数据到数据库(支持 MySQL)的需求，请修改项目根目录下的`config.yaml`以适配你的 MySQL，同时新建`gushiwen`这个数据库，使用 `poem.sql`新建表，可以用`Navicat`一键导入。

## 使用

输入古诗文网的链接，即可爬取该页面所有诗歌的诗名，作者，朝代，内容，译文，注释，赏析，创作背景。

输出的`json`格式如下：

```json
{'name': '行宫', 'author': '元稹', 'dynasty': '唐代', 'content': '寥落古行宫，宫花寂寞红。白头宫女在，闲坐说玄宗。', 'trans': '曾经富丽堂皇的古行宫已是一片荒凉冷落，宫中艳丽的花儿在寂寞寥落中开放。幸存的几个满头白发的宫女，闲坐无事只能谈论着玄宗轶事。', 'annotation': '寥（liáo）落：寂寞冷落。行宫：皇帝在京城之外的宫殿。这里指当时东都洛阳的皇帝行宫上阳宫。宫花：行宫里的花。白头宫女：据白居易《上阳白发人》，一些宫女天宝末年被“潜配”到上阳宫，在这冷宫里一闭四十多年，成了白发宫人。说：谈论。玄宗：指唐玄宗。', 'appreciation': '元稹的这首《行宫》是一首抒发盛衰之感的诗，这首短小精悍的五绝具有深邃的意境，富有隽永的诗味，倾诉了宫女无穷的哀怨之情，寄托了诗人深沉的盛衰之感。诗人先写环境。首句中“寥落”已点出行宫的空虚冷落，又着一“古”字，更显其破旧之象。这样的环境本身就暗示着昔盛今衰的变迁。而后以“宫花寂寞红”续接，此处可见运思缜密。娇艳红花与古旧行宫相映衬，更见行宫“寥落”，加强了时移世迁的盛衰之感。两句景语，令人心无旁骛，只有沉沉的感伤。后两句由景及人，写宫女，“白头”与第二句中的红花相映衬。宫中花开如旧，而当年花容月貌的宫女已变成了白发老妇。物是人非，此间包含着多少哀怨、多少凄凉便不言而喻了。末句“闲”字与上文“寂寞”相照应，写出宫女们长年受冷落的孤寂与无奈。过去她们的一颦一笑、盛装丽服只为取悦君王，而今再无缘见龙颜，她们还能做什么呢？ 只能无聊地“闲”在冷宫。而这些宫女们所谈的仍旧是玄宗盛世。这一方面表现了她们对往昔生活的追忆，另方面也证明了如今无可言说的空虚。比较之下，那种深沉的盛衰之感越发鲜明突出而具体了。这里，寥落古行宫中的白头宫女，还是唐玄宗时代历史的见证人。唐玄宗在其继位后期，宠幸杨贵妃，终日沉溺在淫乐酒色之中，把政务全部委给奸相李林甫和杨国忠，朝纲紊乱，谄佞当道，终于酿成安史之乱。乱后，玄宗被迫退位，赫赫不可一世的大唐王朝亦从此一蹶不振，日益走向下坡路。白居易在《长恨歌》里曾深致感慨说：“缓歌慢舞凝丝竹，尽日君王看不足。渔阳鼙鼓动地来，惊破霓裳羽衣曲。”四句诗，已形象地概括出玄宗昏愦好色与亡国致乱的历史因由，其讽刺与揭露是十分深刻的。元稹这首短诗当然不可能象白诗那样铺张扬厉，极尽渲染之能事，他只能采取对照、暗示点染等方法，把这一段轰轰烈烈的历史高度浓缩，加以典型化的处理，从而让人回味咀嚼。寥落的古行宫，那在寂寞之中随岁月更替而自生自落的宫花，那红颜的少女变为白发老人，都深深地带有时代盛衰迁移的痕迹。白头宫女亲历开元、天宝之世，本身就是历史的见证人，“闲坐说玄宗”的由治而乱。这本是诗篇主旨所在，也是诗人认为应引以为戒的地方，却以貌似悠闲实则深沉的笔调加以表现，语少意多，有无穷之味。二十个字，地点、时间、人物、动作，全都表现出来了，构成了一幅非常生动的画面。这个画面触发读者联翩的浮想：宫女们年轻时都是花容月貌，娇姿艳质，这些美丽的宫女被禁闭在这冷落的古行宫中，成日寂寞无聊，看着宫花，花开花落，年复一年，青春消逝，红颜憔悴，白发频添，如此被摧残，往事岂堪重新回顾！然而，她们被幽闭冷宫，与世隔绝，别无话题，却只能回顾天宝时代玄宗遗事，此景此情，令人凄绝。“寥落”、“寂寞”、“闲坐”，既描绘当时的情景，也反映诗人的倾向。凄凉的身世，哀怨的情怀，盛衰的感慨，二十个字描绘出那样生动的画面，表现出那样深刻的思想。这首诗正是运用以少总多的表现手法，语少意足，有无穷味。另一个表现手法是以乐景写哀情。我国古典诗歌，其所写景物，有时从对立面的角度反衬心理，利用忧思愁苦的心情同良辰美景气氛之间的矛盾，以乐景写哀情，却能收到很好的艺术效果。这首诗也运用了这一手法。诗所要表现的是凄凉哀怨的心境，但却着意描绘红艳的宫花。红花一般是表现热闹场面，烘托欢乐情绪的，但在这里却起了很重要的反衬作用：盛开的红花和寥落的行宫相映衬，加强了时移世迁的盛衰之感；春天的红花和宫女的白发相映衬，表现了红颜易老的人生感慨；红花美景与凄寂心境相映衬，突出了宫女被禁闭的哀怨情绪。红花，在这里起了很大的作用。这都是利用好景致与恶心情的矛盾，来突出中心思想，即王夫之《姜斋诗话》所谓“以乐景写哀”，一倍增其哀。白居易《上阳白发人》“宫莺百啭愁厌闻，梁燕双栖老休妒”，也可以说是以乐写哀。不过白居易的写法直接揭示了乐景写哀情的矛盾，而元稹《行宫》则是以乐景作比较含蓄的反衬，显得更有余味。这首绝句语言平实，但很有概括力，精警动人，也很含蓄，给人以想象的天地，历史沧桑之感尽在不言之中，寓意深刻，自来评价很高。王建的《宫词》，白居易的《长恨歌》，元稹的《连昌宫词》，都是长达千字左右的宏篇巨制，详尽地描述了唐玄宗时代治乱兴衰的历史过程，感叹兴亡。总结教训，内容广博而深刻。元稹这首小诗总共不过二十个字，能入选《唐诗三百首》，与这些长篇巨作比美，可谓短小精悍，字字珠玑。', 'background': '元稹生活在中唐年代，正值唐朝经历过安史之乱不久，国力的各个方面都在走下坡路之时。这首诗可能是他在唐宪宗元和四年（809）作于洛阳。'}
```

### 爬取整页诗歌

例如我要爬取唐诗三百首，先去古诗文网获得唐诗三百首的网址链接：
![image-20240304160226002](https://cdn.jsdelivr.net/gh/palp1tate/ImgPicGo/img/image-20240304160226002.png)

右侧的古诗三百，宋词三百，小学古诗等都可以爬取你只需要拿到链接就可以了。

运行`python shige.py`，结果如下：

![image-20240304160552465](https://cdn.jsdelivr.net/gh/palp1tate/ImgPicGo/img/image-20240304160552465.png)

或者运行`python shige_csv.py`，诗歌数据会写到当前目录的`poem.csv`文件中。该文件使用`utf-8`编码：   

![image-20240409150639588](https://cdn.jsdelivr.net/gh/palp1tate/ImgPicGo/img/image-20240409150639588.png)

或者运行`python shige_db.py`，诗歌数据会保存到`MySQL`数据库中。

![image-20240524160509353](https://cdn.jsdelivr.net/gh/palp1tate/ImgPicGo/img/image-20240524160509353.png)

效果如下：
![image-20240524160612590](https://cdn.jsdelivr.net/gh/palp1tate/ImgPicGo/img/image-20240524160612590.png)

### 爬取单首诗歌

运行`python single_shige.py`或者运行`python single_shige_csv.py`以及`python single_shige_db.py`，注意输入的是单个诗歌的链接，而不是整页的链接。

![image-20240523115443785](https://cdn.jsdelivr.net/gh/palp1tate/ImgPicGo/img/image-20240523115443785.png)

## 声明

本爬虫代码仅可用于个人学习用途，切勿用于任何商业用途！！！

## Stargazers over time
[![Stargazers over time](https://starchart.cc/palp1tate/fetch-gushiwen.svg?variant=light)](https://starchart.cc/palp1tate/fetch-gushiwen)
